#ifndef __USART_H
#define __USART_H

#include <stm32f10x.h>

// ���ڽ��ձ�������
// ����1��K230ͨ�ţ�
extern volatile uint8_t USART1_RxData ;   //K230���ݽ���
extern volatile uint8_t USART1_RxFlag ;   //K230���ձ�־

//k230���յ�������
extern  float pan; 
extern  float tilt;
extern  uint16_t rect_x;
extern  uint16_t rect_y;

extern volatile uint8_t stepmotorx_Flag;
extern volatile uint8_t stepmotory_Flag;


// ����2���������X����ƣ�
extern volatile uint8_t USART2_RxData;   //�������X�����ݽ���
extern volatile uint8_t USART2_RxFlag;   //�������Y����ձ�־
// ����3���������Y����ƣ�
extern volatile uint8_t USART3_RxData;   //�������X�����ݽ���
extern volatile uint8_t USART3_RxFlag;   //�������Y����ձ�־

// ���������Ⱥ�
#define RX_BUF1_SIZE 10
#define RX_BUF2_SIZE 11
#define RX_BUF3_SIZE 11

// ���ջ���������
// ����1��K230ͨ�ţ�
extern volatile uint16_t uart1_rx_buf[RX_BUF1_SIZE];
extern volatile uint8_t uart1_rx_pt;
// ����2���������X����ƣ�
extern volatile uint16_t uart2_rx_buf[RX_BUF2_SIZE];
extern volatile uint8_t uart2_rx_pt;
// ����3���������Y����ƣ�
extern volatile uint16_t uart3_rx_buf[RX_BUF3_SIZE];
extern volatile uint8_t uart3_rx_pt;

// ��ͷ��β��
#define MY_UART1_HEADER 0xFF
#define MY_UART1_TAIL   0xFE
#define MY_UART2_HEADER 0x7B
#define MY_UART2_TAIL   0x7D
#define MY_UART3_HEADER 0x7B
#define MY_UART3_TAIL   0x7D

// ����������ݰ��ṹ��
typedef struct {
    uint8_t header;      // ��ͷ 0x7b
    uint8_t id;          // ������ 0x01/0x02
    uint8_t ctrl_mode;   // ����ģʽ
    uint8_t dir;         // ����
    uint8_t microstep;   // ϸ����
    uint8_t angle_h;     // �Ƕȸ�8λ
    uint8_t angle_l;     // �Ƕȵ�8λ
    uint8_t speed_h;     // �ٶȸ�8λ
    uint8_t speed_l;     // �ٶȵ�8λ
    uint8_t checksum;    // У��λ
    uint8_t tail;        // ��β 0x7d
} StepperPacket_t;

// ����2����������ݰ�
extern StepperPacket_t stepper_packet_2; // ���ڴ���2����
// ����3����������ݰ�
extern StepperPacket_t stepper_packet_3; // ���ڴ���3����

// ��������
void USART_INIT(void);
void UART1_rx_dataframe(void);
void UART2_rx_dataframe(void);
void UART3_rx_dataframe(void);
void USART_SendByte(USART_TypeDef* USARTx, uint8_t Byte);
void USART_SendString(USART_TypeDef* USARTx, char *String);
void USART2_SendStepperPacket(const StepperPacket_t* pkt);
void USART3_SendStepperPacket(const StepperPacket_t* pkt);
void StepperPacket_Fill(StepperPacket_t* pkt, uint8_t id, uint8_t ctrl_mode, uint8_t dir, uint8_t microstep, int16_t angle, uint16_t speed);

#endif
