#include "pid.h"

// 全局PID控制器实例
PID_Controller_t pan_pid;   // 水平方向PID控制器
PID_Controller_t tilt_pid;  // 垂直方向PID控制器

/**
 * 函数名：PID_Init
 * 参数：无
 * 返回值：无
 * 功能：初始化云台追踪PID控制器
 */
void PID_Init(void)
{
    // 初始化水平方向PID控制器
    PID_Controller_Init(&pan_pid, PAN_KP, PAN_KI, PAN_KD, 
                       PID_MAX_OUTPUT, PID_MIN_OUTPUT, 
                       PID_MAX_INTEGRAL, PID_MIN_INTEGRAL);
    
    // 初始化垂直方向PID控制器
    PID_Controller_Init(&tilt_pid, TILT_KP, TILT_KI, TILT_KD,
                       PID_MAX_OUTPUT, PID_MIN_OUTPUT,
                       PID_MAX_INTEGRAL, PID_MIN_INTEGRAL);
    
    // 设置目标值为视野中心
    PID_SetTarget(&pan_pid, CAMERA_CENTER_X);
    PID_SetTarget(&tilt_pid, CAMERA_CENTER_Y);
}

/**
 * 函数名：PID_Controller_Init
 * 参数：pid - PID控制器指针
 *       kp, ki, kd - PID参数
 *       max_out, min_out - 输出限制
 *       max_integral, min_integral - 积分限幅
 * 返回值：无
 * 功能：初始化单个PID控制器
 */
void PID_Controller_Init(PID_Controller_t* pid, float kp, float ki, float kd,
                        float max_out, float min_out, float max_integral, float min_integral)
{
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
    pid->target = 0.0f;
    pid->current = 0.0f;
    pid->error = 0.0f;
    pid->last_error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->output = 0.0f;
    pid->max_output = max_out;
    pid->min_output = min_out;
    pid->max_integral = max_integral;
    pid->min_integral = min_integral;
}

/**
 * 函数名：PID_Calculate
 * 参数：pid - PID控制器指针
 *       target - 目标值
 *       current - 当前值
 * 返回值：PID输出值
 * 功能：计算PID输出
 */
float PID_Calculate(PID_Controller_t* pid, float target, float current)
{
    // 更新目标值和当前值
    pid->target = target;
    pid->current = current;
    
    // 计算误差
    pid->error = pid->target - pid->current;
    
    // 计算积分项
    pid->integral += pid->error;
    
    // 积分限幅
    if (pid->integral > pid->max_integral) {
        pid->integral = pid->max_integral;
    } else if (pid->integral < pid->min_integral) {
        pid->integral = pid->min_integral;
    }
    
    // 计算微分项
    pid->derivative = pid->error - pid->last_error;
    
    // 计算PID输出
    pid->output = pid->kp * pid->error + 
                  pid->ki * pid->integral + 
                  pid->kd * pid->derivative;
    
    // 输出限幅
    if (pid->output > pid->max_output) {
        pid->output = pid->max_output;
    } else if (pid->output < pid->min_output) {
        pid->output = pid->min_output;
    }
    
    // 保存当前误差作为下次的上次误差
    pid->last_error = pid->error;
    
    return pid->output;
}

/**
 * 函数名：PID_Reset
 * 参数：pid - PID控制器指针
 * 返回值：无
 * 功能：重置PID控制器状态
 */
void PID_Reset(PID_Controller_t* pid)
{
    pid->error = 0.0f;
    pid->last_error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->output = 0.0f;
}

/**
 * 函数名：PID_SetTarget
 * 参数：pid - PID控制器指针
 *       target - 新的目标值
 * 返回值：无
 * 功能：设置PID控制器目标值
 */
void PID_SetTarget(PID_Controller_t* pid, float target)
{
    pid->target = target;
}

/**
 * 函数名：PID_UpdateParams
 * 参数：pid - PID控制器指针
 *       kp, ki, kd - 新的PID参数
 * 返回值：无
 * 功能：更新PID参数
 */
void PID_UpdateParams(PID_Controller_t* pid, float kp, float ki, float kd)
{
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
}

/**
 * 函数名：Gimbal_Tracking_Control
 * 参数：rect_x, rect_y - 矩形中心坐标
 * 返回值：无
 * 功能：云台追踪控制主函数
 */
void Gimbal_Tracking_Control(uint16_t rect_x, uint16_t rect_y)
{
    // 使用PID控制器计算输出
    // 水平方向：目标是视野中心X坐标，当前值是矩形中心X坐标
    PID_Calculate(&pan_pid, CAMERA_CENTER_X, (float)rect_x);
    
    // 垂直方向：目标是视野中心Y坐标，当前值是矩形中心Y坐标  
    PID_Calculate(&tilt_pid, CAMERA_CENTER_Y, (float)rect_y);
}

/**
 * 函数名：Get_Pan_Command
 * 参数：无
 * 返回值：水平方向步进电机控制命令
 * 功能：获取水平方向步进电机控制命令
 */
int16_t Get_Pan_Command(void)
{
    // 将PID输出转换为步进电机命令
    // 这里可以根据实际需要调整比例系数
    return (int16_t)(pan_pid.output * 0.1f);  // 0.1为比例系数，可根据实际调整
}

/**
 * 函数名：Get_Tilt_Command
 * 参数：无
 * 返回值：垂直方向步进电机控制命令
 * 功能：获取垂直方向步进电机控制命令
 */
int16_t Get_Tilt_Command(void)
{
    // 将PID输出转换为步进电机命令
    // 这里可以根据实际需要调整比例系数
    return (int16_t)(tilt_pid.output * 0.1f);  // 0.1为比例系数，可根据实际调整
}

/**
 * 函数名：Get_Pan_Error
 * 参数：无
 * 返回值：水平方向误差值
 * 功能：获取水平方向PID误差值，用于调试
 */
float Get_Pan_Error(void)
{
    return pan_pid.error;
}

/**
 * 函数名：Get_Tilt_Error
 * 参数：无
 * 返回值：垂直方向误差值
 * 功能：获取垂直方向PID误差值，用于调试
 */
float Get_Tilt_Error(void)
{
    return tilt_pid.error;
}

/**
 * 函数名：Get_Pan_Output
 * 参数：无
 * 返回值：水平方向PID输出值
 * 功能：获取水平方向PID输出值，用于调试
 */
float Get_Pan_Output(void)
{
    return pan_pid.output;
}

/**
 * 函数名：Get_Tilt_Output
 * 参数：无
 * 返回值：垂直方向PID输出值
 * 功能：获取垂直方向PID输出值，用于调试
 */
float Get_Tilt_Output(void)
{
    return tilt_pid.output;
}

/**
 * 函数名：PID_Tune_Pan
 * 参数：kp, ki, kd - 新的PID参数
 * 返回值：无
 * 功能：在线调整水平方向PID参数
 */
void PID_Tune_Pan(float kp, float ki, float kd)
{
    PID_UpdateParams(&pan_pid, kp, ki, kd);
    // 重置积分项，避免参数改变时的积分饱和
    pan_pid.integral = 0.0f;
}

/**
 * 函数名：PID_Tune_Tilt
 * 参数：kp, ki, kd - 新的PID参数
 * 返回值：无
 * 功能：在线调整垂直方向PID参数
 */
void PID_Tune_Tilt(float kp, float ki, float kd)
{
    PID_UpdateParams(&tilt_pid, kp, ki, kd);
    // 重置积分项，避免参数改变时的积分饱和
    tilt_pid.integral = 0.0f;
}
