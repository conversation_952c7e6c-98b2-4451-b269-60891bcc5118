#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "nvic.h"
#include "usart.h"
#include "pid.h"

int main(void)
{
	USART_INIT(); //��ʼ������
	NVIC_INIT(); //����NVIC���ȼ����鼰ʹ�ܴ�������ж�
	PID_Init(); //��ʼ��PID������

	// ע�͵�ԭ���ļ򵥿��Ʊ�������PID����
	// float pan_raw = 0; //��̨ˮƽ�Ƕȣ�����������λ���ȣ�
	// float tilt_raw = 0; //��̨��ֱ�Ƕȣ�����������λ���ȣ�

	int pan_command = 0; //��̨ˮƽ�Ƕ�����ֵ
	int tilt_command = 0; //��̨��ֱ�Ƕ�����ֵ
	
	while (1)
	{
		// ʹ��PID������������̨׷�ٿ���
		if(stepmotorx_Flag || stepmotory_Flag) // ���յ�K230���ݺ�
		{
			// ʹ��PID������������̨׷�ٿ���
			Gimbal_Tracking_Control(rect_x, rect_y);

			// ��ȡPID�������
			pan_command = Get_Pan_Command();
			tilt_command = Get_Tilt_Command();
		}

		if(stepmotorx_Flag) //����������X����Ʊ�־������
		{
			if(pan_command > 0) //�����Ҫ�ƶ�
			{
				StepperPacket_Fill(&stepper_packet_2,0x01,0x02,0x01,0x20,pan_command,5); //��䲽�����2���ݰ�
			}
			else if(pan_command < 0) //�����Ҫ�����ƶ�
			{
				StepperPacket_Fill(&stepper_packet_2,0x01,0x02,0x00,0x20,-pan_command,5); //��䲽�����2���ݰ�
			}
			else // pan_command == 0������Ҫ�ƶ�
			{
				// ���Ͳ������2���ݰ�������Ϊ0
				StepperPacket_Fill(&stepper_packet_2,0x01,0x02,0x01,0x20,0,5);
			}
			USART2_SendStepperPacket(&stepper_packet_2); //���Ͳ������2���ݰ�
			// ����������X����Ʊ�־
			stepmotorx_Flag = 0;
		}
		if(stepmotory_Flag) //����������Y����Ʊ�־������
		{
			if(tilt_command > 0) //�����Ҫ�ƶ�
			{
				StepperPacket_Fill(&stepper_packet_3,0x02,0x02,0x01,0x20,tilt_command,10); //��䲽�����3���ݰ�
			}
			else if(tilt_command < 0) //�����Ҫ�����ƶ�
			{
				StepperPacket_Fill(&stepper_packet_3,0x02,0x02,0x00,0x20,-tilt_command,10); //��䲽�����3���ݰ�
			}
			else // tilt_command == 0������Ҫ�ƶ�
			{
				// ���Ͳ������3���ݰ�������Ϊ0
				StepperPacket_Fill(&stepper_packet_3,0x02,0x02,0x01,0x20,0,10);
			}
			USART3_SendStepperPacket(&stepper_packet_3); //���Ͳ������3���ݰ�
			// ����������Y����Ʊ�־
			stepmotory_Flag = 0;
		}
	}
	
}
	
