#ifndef __PID_H
#define __PID_H

#include <stm32f10x.h>

// PID控制器结构体
typedef struct {
    float kp;           // 比例系数
    float ki;           // 积分系数  
    float kd;           // 微分系数
    float target;       // 目标值
    float current;      // 当前值
    float error;        // 当前误差
    float last_error;   // 上次误差
    float integral;     // 积分累积
    float derivative;   // 微分值
    float output;       // PID输出
    float max_output;   // 最大输出限制
    float min_output;   // 最小输出限制
    float max_integral; // 积分限幅
    float min_integral; // 积分限幅
} PID_Controller_t;

// 云台追踪PID控制器
extern PID_Controller_t pan_pid;   // 水平方向PID控制器
extern PID_Controller_t tilt_pid;  // 垂直方向PID控制器

// 视野中心坐标（目标值）
#define CAMERA_CENTER_X 320  // 假设K230视野宽度为640，中心为320
#define CAMERA_CENTER_Y 240  // 假设K230视野高度为480，中心为240

// PID参数宏定义
// 水平方向PID参数
#define PAN_KP  2.0f    // 比例系数
#define PAN_KI  0.1f    // 积分系数
#define PAN_KD  0.5f    // 微分系数

// 垂直方向PID参数  
#define TILT_KP 2.0f    // 比例系数
#define TILT_KI 0.1f    // 积分系数
#define TILT_KD 0.5f    // 微分系数

// 输出限制
#define PID_MAX_OUTPUT  1000.0f  // 最大输出角度
#define PID_MIN_OUTPUT -1000.0f  // 最小输出角度

// 积分限幅
#define PID_MAX_INTEGRAL  500.0f
#define PID_MIN_INTEGRAL -500.0f

// 函数声明
void PID_Init(void);
void PID_Controller_Init(PID_Controller_t* pid, float kp, float ki, float kd, 
                        float max_out, float min_out, float max_integral, float min_integral);
float PID_Calculate(PID_Controller_t* pid, float target, float current);
void PID_Reset(PID_Controller_t* pid);
void PID_SetTarget(PID_Controller_t* pid, float target);
void PID_UpdateParams(PID_Controller_t* pid, float kp, float ki, float kd);

// 云台追踪控制函数
void Gimbal_Tracking_Control(uint16_t rect_x, uint16_t rect_y);
int16_t Get_Pan_Command(void);
int16_t Get_Tilt_Command(void);

// 调试和监控函数
void PID_Debug_Print(void);
float Get_Pan_Error(void);
float Get_Tilt_Error(void);
float Get_Pan_Output(void);
float Get_Tilt_Output(void);

// 在线参数调整函数
void PID_Tune_Pan(float kp, float ki, float kd);
void PID_Tune_Tilt(float kp, float ki, float kd);

#endif
