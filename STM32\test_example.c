/**
 * PID云台追踪系统测试示例
 * 
 * 这个文件展示了如何使用PID控制系统进行云台追踪
 * 注意：这只是示例代码，不要直接编译到项目中
 */

#include "stm32f10x.h"
#include "pid.h"
#include "usart.h"

// 测试函数：模拟K230发送不同位置的矩形中心坐标
void Test_PID_Response(void)
{
    // 测试场景1：矩形在视野左上角
    uint16_t test_x = 160;  // 偏左
    uint16_t test_y = 120;  // 偏上
    
    printf("测试场景1：矩形在视野左上角 (160, 120)\n");
    Gimbal_Tracking_Control(test_x, test_y);
    
    printf("Pan误差: %.2f, 输出: %.2f, 命令: %d\n", 
           Get_Pan_Error(), Get_Pan_Output(), Get_Pan_Command());
    printf("Tilt误差: %.2f, 输出: %.2f, 命令: %d\n", 
           Get_Tilt_Error(), Get_Tilt_Output(), Get_Tilt_Command());
    
    // 测试场景2：矩形在视野右下角
    test_x = 480;  // 偏右
    test_y = 360;  // 偏下
    
    printf("\n测试场景2：矩形在视野右下角 (480, 360)\n");
    Gimbal_Tracking_Control(test_x, test_y);
    
    printf("Pan误差: %.2f, 输出: %.2f, 命令: %d\n", 
           Get_Pan_Error(), Get_Pan_Output(), Get_Pan_Command());
    printf("Tilt误差: %.2f, 输出: %.2f, 命令: %d\n", 
           Get_Tilt_Error(), Get_Tilt_Output(), Get_Tilt_Command());
    
    // 测试场景3：矩形在视野中心
    test_x = 320;  // 中心
    test_y = 240;  // 中心
    
    printf("\n测试场景3：矩形在视野中心 (320, 240)\n");
    Gimbal_Tracking_Control(test_x, test_y);
    
    printf("Pan误差: %.2f, 输出: %.2f, 命令: %d\n", 
           Get_Pan_Error(), Get_Pan_Output(), Get_Pan_Command());
    printf("Tilt误差: %.2f, 输出: %.2f, 命令: %d\n", 
           Get_Tilt_Error(), Get_Tilt_Output(), Get_Tilt_Command());
}

// 测试函数：PID参数调整示例
void Test_PID_Tuning(void)
{
    printf("\n=== PID参数调整测试 ===\n");
    
    // 原始参数测试
    printf("原始参数 - Pan: Kp=%.1f, Ki=%.1f, Kd=%.1f\n", 
           PAN_KP, PAN_KI, PAN_KD);
    
    uint16_t test_x = 400;  // 偏右80像素
    uint16_t test_y = 200;  // 偏上40像素
    
    Gimbal_Tracking_Control(test_x, test_y);
    printf("测试位置(400,200) - Pan输出: %.2f, Tilt输出: %.2f\n", 
           Get_Pan_Output(), Get_Tilt_Output());
    
    // 调整参数：增大比例系数
    printf("\n调整参数 - 增大Kp到3.0\n");
    PID_Tune_Pan(3.0f, PAN_KI, PAN_KD);
    PID_Tune_Tilt(3.0f, TILT_KI, TILT_KD);
    
    Gimbal_Tracking_Control(test_x, test_y);
    printf("调整后 - Pan输出: %.2f, Tilt输出: %.2f\n", 
           Get_Pan_Output(), Get_Tilt_Output());
    
    // 恢复原始参数
    PID_Tune_Pan(PAN_KP, PAN_KI, PAN_KD);
    PID_Tune_Tilt(TILT_KP, TILT_KI, TILT_KD);
}

// 测试函数：模拟连续追踪过程
void Test_Continuous_Tracking(void)
{
    printf("\n=== 连续追踪测试 ===\n");
    
    // 模拟矩形从左到右移动
    uint16_t positions_x[] = {100, 150, 200, 250, 300, 350, 400, 450, 500};
    uint16_t positions_y[] = {240, 240, 240, 240, 240, 240, 240, 240, 240}; // Y保持中心
    
    int num_positions = sizeof(positions_x) / sizeof(positions_x[0]);
    
    printf("模拟矩形从左到右移动（Y轴保持中心）:\n");
    printf("位置\t\tPan误差\tPan输出\tTilt误差\tTilt输出\n");
    
    for(int i = 0; i < num_positions; i++)
    {
        Gimbal_Tracking_Control(positions_x[i], positions_y[i]);
        
        printf("(%d,%d)\t\t%.1f\t%.1f\t%.1f\t\t%.1f\n",
               positions_x[i], positions_y[i],
               Get_Pan_Error(), Get_Pan_Output(),
               Get_Tilt_Error(), Get_Tilt_Output());
        
        // 模拟延时，实际使用中由K230数据接收频率决定
        // Delay_ms(100);
    }
}

// 主测试函数
void Run_PID_Tests(void)
{
    printf("=== K230云台追踪PID控制系统测试 ===\n\n");
    
    // 初始化PID系统
    PID_Init();
    printf("PID系统初始化完成\n");
    printf("视野中心设置为: (%d, %d)\n", CAMERA_CENTER_X, CAMERA_CENTER_Y);
    printf("PID参数 - Pan: Kp=%.1f, Ki=%.1f, Kd=%.1f\n", PAN_KP, PAN_KI, PAN_KD);
    printf("PID参数 - Tilt: Kp=%.1f, Ki=%.1f, Kd=%.1f\n\n", TILT_KP, TILT_KI, TILT_KD);
    
    // 运行各种测试
    Test_PID_Response();
    Test_PID_Tuning();
    Test_Continuous_Tracking();
    
    printf("\n=== 测试完成 ===\n");
}

/**
 * 使用说明：
 * 
 * 1. 在main.c中调用Run_PID_Tests()来运行测试
 * 2. 通过串口查看测试输出结果
 * 3. 根据测试结果调整PID参数
 * 4. 实际使用时，删除测试代码，只保留PID控制逻辑
 * 
 * 参数调整建议：
 * - 如果响应太慢，增大Kp
 * - 如果有振荡，减小Kp或增大Kd
 * - 如果有稳态误差，增大Ki
 * - 如果超调严重，增大Kd
 */
