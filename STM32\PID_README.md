# K230云台追踪PID控制系统

## 系统概述

本系统实现了基于PID控制器的K230视觉追踪云台控制。系统通过分析K230发送的矩形中心坐标，使用PID算法计算云台调整角度，使K230视野始终追踪矩形中心。

## 系统架构

```
K230 -> USART1 -> STM32 -> PID控制器 -> USART2/3 -> 步进电机云台
```

### 数据流程
1. K230检测矩形并发送中心坐标(rect_x, rect_y)到STM32
2. STM32接收数据并触发PID控制计算
3. PID控制器计算云台调整角度
4. STM32发送控制命令到步进电机驱动器

## PID控制器配置

### 当前参数设置
```c
// 水平方向PID参数
#define PAN_KP  2.0f    // 比例系数
#define PAN_KI  0.1f    // 积分系数
#define PAN_KD  0.5f    // 微分系数

// 垂直方向PID参数  
#define TILT_KP 2.0f    // 比例系数
#define TILT_KI 0.1f    // 积分系数
#define TILT_KD 0.5f    // 微分系数
```

### 视野中心设置
```c
#define CAMERA_CENTER_X 320  // 视野宽度640的中心
#define CAMERA_CENTER_Y 240  // 视野高度480的中心
```

## 参数调整指南

### PID参数调整原则

1. **比例系数(Kp)**
   - 增大Kp：响应速度快，但可能产生超调和振荡
   - 减小Kp：响应平稳，但可能响应慢、稳态误差大
   - 建议范围：1.0 - 5.0

2. **积分系数(Ki)**
   - 增大Ki：消除稳态误差，但可能产生积分饱和
   - 减小Ki：减少超调，但稳态误差可能增大
   - 建议范围：0.01 - 0.5

3. **微分系数(Kd)**
   - 增大Kd：减少超调，提高稳定性
   - 减小Kd：减少噪声敏感性
   - 建议范围：0.1 - 1.0

### 调试步骤

1. **初始设置**
   - 设置Kp=1.0, Ki=0, Kd=0
   - 观察系统响应

2. **调整比例系数**
   - 逐步增大Kp直到出现轻微振荡
   - 然后减小到振荡消失

3. **添加微分控制**
   - 增加Kd来减少超调
   - 通常Kd = Kp/4 是一个好的起点

4. **添加积分控制**
   - 最后添加少量Ki来消除稳态误差
   - 注意避免积分饱和

## 代码修改说明

### 主要修改文件

1. **STM32/Hardware/pid.h** - PID控制器头文件
2. **STM32/Hardware/pid.c** - PID控制器实现
3. **STM32/User/main.c** - 主程序集成PID控制
4. **STM32/Hardware/usart.h** - 添加函数声明

### 关键函数

- `PID_Init()` - 初始化PID控制器
- `Gimbal_Tracking_Control()` - 云台追踪控制主函数
- `Get_Pan_Command()` / `Get_Tilt_Command()` - 获取电机控制命令
- `PID_UpdateParams()` - 动态调整PID参数

## 性能优化建议

1. **参数微调**
   - 根据实际云台响应特性调整PID参数
   - 可以为水平和垂直方向设置不同的参数

2. **输出限制**
   - 调整`PID_MAX_OUTPUT`和`PID_MIN_OUTPUT`
   - 防止电机过度运动

3. **积分限幅**
   - 设置合适的积分限幅值
   - 防止积分饱和

4. **滤波处理**
   - 可以对输入信号进行滤波
   - 减少噪声对控制的影响

## 故障排除

### 常见问题

1. **系统振荡**
   - 减小Kp或增大Kd
   - 检查机械系统是否有松动

2. **响应慢**
   - 增大Kp
   - 检查是否有稳态误差

3. **超调严重**
   - 减小Kp，增大Kd
   - 检查积分项是否过大

4. **稳态误差**
   - 增大Ki
   - 检查目标值设置是否正确

### 调试输出

使用以下函数获取调试信息：
- `Get_Pan_Error()` - 获取水平误差
- `Get_Tilt_Error()` - 获取垂直误差  
- `Get_Pan_Output()` - 获取水平输出
- `Get_Tilt_Output()` - 获取垂直输出

## 注意事项

1. 确保K230发送的坐标数据格式正确
2. 步进电机驱动器参数需要与PID输出匹配
3. 定期检查机械系统的精度和稳定性
4. 根据实际应用场景调整视野中心坐标
