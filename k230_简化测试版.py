import time, sys
import math
from media.sensor import *
from media.display import *
from media.media import *
from machine import UART, FPIOA

print("=== K230云台追踪系统 - 简化测试版 ===")

# --------------------------- 硬件初始化 ---------------------------
# 串口初始化
try:
    fpioa = FPIOA()
    fpioa.set_function(3, FPIOA.UART1_TXD)
    fpioa.set_function(4, FPIOA.UART1_RXD)
    uart = UART(UART.UART1, 115200)
    print("串口初始化成功")
except Exception as e:
    print(f"串口初始化失败: {e}")
    sys.exit()

# 屏幕分辨率设置
lcd_width = 800
lcd_height = 480

# 摄像头初始化（简化版本）
def init_camera_simple():
    """简化的摄像头初始化"""
    global sensor
    try:
        print("正在初始化摄像头...")
        
        # 先初始化MediaManager
        MediaManager.init()
        print("MediaManager初始化成功")
        
        # 初始化显示
        Display.init(Display.ST7701, width=lcd_width, height=lcd_height, to_ide=True)
        print("显示初始化成功")
        
        # 初始化传感器
        sensor = Sensor(width=1280, height=960)
        sensor.reset()
        print("传感器重置成功")
        
        # 设置参数
        sensor.set_framesize(width=320, height=240)
        sensor.set_pixformat(Sensor.RGB565)
        print("传感器参数设置成功")
        
        # 启动传感器
        time.sleep_ms(500)  # 增加延时
        sensor.run()
        print("传感器启动成功")
        
        # 等待稳定
        time.sleep_ms(1000)
        print("等待传感器稳定...")
        
        # 测试拍照
        for i in range(3):
            try:
                test_img = sensor.snapshot()
                if test_img is not None:
                    print(f"测试拍照成功 (尝试 {i+1}/3)")
                    return True
                else:
                    print(f"测试拍照返回None (尝试 {i+1}/3)")
            except Exception as e:
                print(f"测试拍照失败 (尝试 {i+1}/3): {e}")
            time.sleep_ms(500)
            
        return False
        
    except Exception as e:
        print(f"摄像头初始化失败: {e}")
        return False

# 尝试初始化摄像头
if not init_camera_simple():
    print("无法初始化摄像头，程序退出")
    sys.exit()

# --------------------------- 简化的工具函数 ---------------------------
def send_test_data_to_stm32(rect_x, rect_y):
    """发送测试数据到STM32"""
    try:
        # 简化的数据包格式
        pan = 1000 + int(rect_x * 150 / 320)
        tilt = 1000 + int(rect_y * 10 / 240)
        
        data = bytearray([
            0xFF,  # 帧头
            (pan >> 8) & 0xFF, pan & 0xFF,
            (tilt >> 8) & 0xFF, tilt & 0xFF,
            (rect_x >> 8) & 0xFF, rect_x & 0xFF,
            (rect_y >> 8) & 0xFF, rect_y & 0xFF,
            0xFE   # 帧尾
        ])
        
        uart.write(data)
        print(f"发送: 中心({rect_x}, {rect_y}) -> pan={pan}, tilt={tilt}")
        return True
    except Exception as e:
        print(f"发送数据失败: {e}")
        return False

def safe_snapshot():
    """安全拍照函数"""
    try:
        img = sensor.snapshot()
        return img
    except Exception as e:
        print(f"拍照错误: {e}")
        return None

# --------------------------- 简化主循环 ---------------------------
print("开始主循环...")
clock = time.clock()
frame_count = 0
error_count = 0
max_errors = 10

# 测试模式：发送固定的中心坐标
test_mode = True
test_x, test_y = 160, 120  # 测试坐标

while True:
    try:
        clock.tick()
        frame_count += 1
        
        # 拍照
        img = safe_snapshot()
        if img is None:
            error_count += 1
            if error_count > max_errors:
                print("拍照失败次数过多，退出")
                break
            continue
        
        # 重置错误计数
        error_count = 0
        
        # 在图像上绘制一些基本信息
        fps = clock.fps()
        img.draw_string_advanced(10, 10, 20, f"FPS: {fps:.1f}", color=(255, 255, 255))
        img.draw_string_advanced(10, 40, 20, f"Frame: {frame_count}", color=(255, 255, 255))
        
        # 绘制视野中心十字线
        center_x, center_y = 160, 120  # 320x240的中心
        img.draw_line(center_x-20, center_y, center_x+20, center_y, color=(0, 255, 0), thickness=2)
        img.draw_line(center_x, center_y-20, center_x, center_y+20, color=(0, 255, 0), thickness=2)
        
        # 测试模式：绘制测试点并发送数据
        if test_mode:
            img.draw_circle(test_x, test_y, 10, color=(255, 0, 0), thickness=2)
            img.draw_string_advanced(test_x+15, test_y-10, 16, "TEST", color=(255, 0, 0))
            
            # 每10帧发送一次数据
            if frame_count % 10 == 0:
                send_test_data_to_stm32(test_x, test_y)
                # 移动测试点
                test_x = (test_x + 20) % 320
                if test_x == 0:
                    test_y = (test_y + 20) % 240
        
        # 显示图像
        try:
            Display.show_image(img,
                              x=round((lcd_width-sensor.width())/2),
                              y=round((lcd_height-sensor.height())/2))
        except Exception as e:
            print(f"显示错误: {e}")
        
        # 每100帧打印一次状态
        if frame_count % 100 == 0:
            print(f"运行正常 - 帧数: {frame_count}, FPS: {fps:.1f}")
        
    except KeyboardInterrupt:
        print("用户中断程序")
        break
    except Exception as e:
        error_count += 1
        print(f"主循环错误 {error_count}/{max_errors}: {e}")
        
        if error_count >= max_errors:
            print("错误过多，程序退出")
            break
        
        time.sleep_ms(100)

print("程序结束")

# 清理资源
try:
    MediaManager.deinit()
    print("资源清理完成")
except:
    pass
