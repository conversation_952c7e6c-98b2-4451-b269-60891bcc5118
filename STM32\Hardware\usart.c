#include "stm32f10x.h"
#include "usart.h"

// �������ȫ�ֱ�������
volatile uint8_t USART1_RxData = 0;    // ����1���յĵ�ǰ�ֽ�����
volatile uint8_t USART1_RxFlag = 0;    // ����1������ɱ�־
volatile uint8_t USART2_RxData = 0;    // ����2���յĵ�ǰ�ֽ�����
volatile uint8_t USART2_RxFlag = 0;    // ����2������ɱ�־
volatile uint8_t USART3_RxData = 0;    // ����3���յĵ�ǰ�ֽ�����
volatile uint8_t USART3_RxFlag = 0;    // ����3������ɱ�־
float pan = 0;                         // ��̨ˮƽ�Ƕȣ�����������λ���ȣ�
float tilt = 0;                        // ��̨��ֱ�Ƕȣ�����������λ���ȣ�
uint16_t rect_x = 0;                   // Ŀ������X���꣨������
uint16_t rect_y = 0;                   // Ŀ������Y���꣨������
volatile uint16_t uart1_rx_buf[RX_BUF1_SIZE] = {0}; // ����1���ջ�����
volatile uint16_t uart2_rx_buf[RX_BUF2_SIZE] = {0}; // ����2���ջ�����
volatile uint16_t uart3_rx_buf[RX_BUF3_SIZE] = {0}; // ����3���ջ�����
volatile uint8_t uart1_rx_pt = 0;      // ����1���ջ�����д��ָ��
volatile uint8_t uart2_rx_pt = 0;      // ����2���ջ�����д��ָ��
volatile uint8_t uart3_rx_pt = 0;      // ����3���ջ�����д��ָ��
StepperPacket_t stepper_packet_2; // ����2����������ݰ�
StepperPacket_t stepper_packet_3; // ����3����������ݰ�
volatile uint8_t stepmotorx_Flag = 0; // �������X����Ʊ�־
volatile uint8_t stepmotory_Flag = 0; // �������Y����Ʊ�־

void USART_INIT(void)
{
    //����USART1ʱ��
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);
    //����usart2ʱ��
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
    //����usart3ʱ��
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3, ENABLE);
    //����GPIOAʱ��
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    //����GPIOBʱ��
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);  

    //����GPIOA����
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;//�����������
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9; //USART1_TX
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure); //��PA9���ų�ʼ��
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU; //��������
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10; //USART1_RX
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure); //��PA10���ų�ʼ��
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP; //�����������
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2; //USART2_TX
    GPIO_Init(GPIOA, &GPIO_InitStructure); //��PA2���ų�ʼ��
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING; //��������
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3; //USART2_RX
    GPIO_Init(GPIOA, &GPIO_InitStructure); //��PA3���ų�ʼ��
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP; //�����������
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10; //USART3_TX
    GPIO_Init(GPIOB, &GPIO_InitStructure); //��PB10���ų�ʼ��
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING; //��������
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_11; //USART3_RX
    GPIO_Init(GPIOB, &GPIO_InitStructure); //��PB11���ų�ʼ��

    //����USART1
    USART_InitTypeDef USART_InitStructure;
    USART_InitStructure.USART_BaudRate = 115200; //������
    USART_InitStructure.USART_WordLength = USART_WordLength_8b; //�ֳ�
    USART_InitStructure.USART_StopBits = USART_StopBits_1; //ֹͣλ
    USART_InitStructure.USART_Parity = USART_Parity_No; //����żУ��
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None; //��Ӳ��������
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx; //���պͷ���ģʽ
    USART_Init(USART1, &USART_InitStructure);
    //����USART2
    USART_InitStructure.USART_BaudRate = 115200; //������
    USART_InitStructure.USART_WordLength = USART_WordLength_8b; //�ֳ�
    USART_InitStructure.USART_StopBits = USART_StopBits_1; //ֹͣλ
    USART_InitStructure.USART_Parity = USART_Parity_No; //����żУ��
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None; //��Ӳ��������
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx; //���պͷ���ģʽ
    USART_Init(USART2, &USART_InitStructure);
    //����USART3
    USART_InitStructure.USART_BaudRate = 115200; //������
    USART_InitStructure.USART_WordLength = USART_WordLength_8b; //�ֳ�
    USART_InitStructure.USART_StopBits = USART_StopBits_1; //ֹͣλ
    USART_InitStructure.USART_Parity = USART_Parity_No; //����żУ��
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None; //��Ӳ��������
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx; //���պͷ���ģʽ
    USART_Init(USART3, &USART_InitStructure);
    
    //ʹ��USART1
    USART_Cmd(USART1, ENABLE);
    //ʹ��USART2
    USART_Cmd(USART2, ENABLE); 
    //ʹ��USART3
    USART_Cmd(USART3, ENABLE);
}

/**
  * ��    �������ڷ���һ���ֽ�
  * ��    ����Byte Ҫ���͵�һ���ֽ�
  * �� �� ֵ����
  */
 void USART_SendByte(USART_TypeDef* USARTx, uint8_t Byte)
{
    while (USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET); //�ȴ��������
    USART_SendData(USARTx, Byte); //���ֽ�����д�����ݼĴ�����д���USART�Զ�����ʱ����
    while (USART_GetFlagStatus(USARTx, USART_FLAG_TC) == RESET); //�ȴ��������
}
/**
  * ��    �������ڷ����ַ���
  * ��    ����String Ҫ���͵��ַ���
  * �� �� ֵ����
  */
void USART_SendString(USART_TypeDef* USARTx, char *String)
{
    while (*String) //�����ַ�����ֱ�������ַ���������'\0'
    {
        USART_SendByte(USARTx, *String++); //����ÿ���ַ�
    }
}

/**
  * ��    ��������1��������֡����
  * ��    ������
  * �� �� ֵ����
  * ��    �ܣ������Զ����ͷ��β����һ������
  */
void UART1_rx_dataframe()
{
    USART1_RxData = USART_ReceiveData(USART1);; //����һ���ֽ�����
    if (USART1_RxData == MY_UART1_HEADER) //�ж��Ƿ�Ϊ��ͷ
    {
        uart1_rx_pt = 0; //ָ�����
        uart1_rx_buf[uart1_rx_pt++] = USART1_RxData; //�����ͷ
    } 
    else if (USART1_RxData == MY_UART1_TAIL && uart1_rx_pt > 0) //�ж��Ƿ�Ϊ��β���ѿ�ʼ����
    {
        uart1_rx_buf[uart1_rx_pt++] = USART1_RxData; //�����β
        // ��ʱ uart1_rx_buf �����һ֡�������ݣ����Խ�һ������
        pan = (uart1_rx_buf[1] << 8) | uart1_rx_buf[2]; //X����Ҫ�ƶ��ĽǶ�
        tilt = (uart1_rx_buf[3] << 8) | uart1_rx_buf[4]; 
        rect_x = (uart1_rx_buf[5] << 8) | uart1_rx_buf[6]; 
        rect_y = (uart1_rx_buf[7] << 8) | uart1_rx_buf[8];
        stepmotorx_Flag = 1; // ���ò������X����Ʊ�־
        stepmotory_Flag = 1; // ���ò������Y����Ʊ�־ 
        uart1_rx_pt = 0; //ָ����㣬׼����һ֡
    } 
    else //�м�����
    {
        uart1_rx_buf[uart1_rx_pt++] = USART1_RxData; //��������
        //uart1_rx_pt %= RX_BUF1_SIZE; //��ֹ���;
    }
}

/**
  * ��    ��������2��������֡����
  * ��    ������
  * �� �� ֵ����
  * ��    �ܣ������Զ����ͷ��β����һ������
  */
void UART2_rx_dataframe()
{
    USART2_RxData = USART_ReceiveData(USART2); //����һ���ֽ�����
    if (USART2_RxData == MY_UART2_HEADER) //�ж��Ƿ�Ϊ��ͷ
    {
        uart2_rx_pt = 0; //ָ�����
        uart2_rx_buf[uart2_rx_pt++] = USART2_RxData; //�����ͷ
    } 
    else if (USART2_RxData == MY_UART2_TAIL && uart2_rx_pt > 0) //�ж��Ƿ�Ϊ��β���ѿ�ʼ����
    {
        uart2_rx_buf[uart2_rx_pt++] = USART2_RxData; //�����β
        // ��ʱ uart2_rx_buf �����һ֡�������ݣ����Խ�һ������
        uart2_rx_pt = 0; //ָ����㣬׼����һ֡
    } 
    else  //�м�����
    {
        uart2_rx_buf[uart2_rx_pt++] = USART2_RxData; //��������
        uart2_rx_pt %= RX_BUF2_SIZE; //��ֹ���;
    }
}

/**
  * ��    ��������3��������֡����
  * ��    ������
  * �� �� ֵ����
  * ��    �ܣ������Զ����ͷ��β����һ������
  */
void UART3_rx_dataframe()
{
    USART3_RxData = USART_ReceiveData(USART3); //����һ���ֽ�����
    if (USART3_RxData == MY_UART3_HEADER) //�ж��Ƿ�Ϊ��ͷ
    {
        uart3_rx_pt = 0; //ָ�����
        uart3_rx_buf[uart3_rx_pt++] = USART3_RxData; //�����ͷ
    } 
    else if (USART3_RxData == MY_UART3_TAIL && uart3_rx_pt > 0) //�ж��Ƿ�Ϊ��β���ѿ�ʼ����
    {
        uart3_rx_buf[uart3_rx_pt++] = USART3_RxData; //�����β
        // ��ʱ uart3_rx_buf �����һ֡�������ݣ����Խ�һ������
        uart3_rx_pt = 0; //ָ����㣬׼����һ֡
    } 
    else //�м�����
    {
        uart3_rx_buf[uart3_rx_pt++] = USART3_RxData; //��������
        uart3_rx_pt %= RX_BUF3_SIZE; //��ֹ���;
    }
}

/**
  * ��    ����USART1�жϺ���
  * ��    ������
  * �� �� ֵ����
  * ע������˺���Ϊ�жϺ�����������ã��жϴ������Զ�ִ��
  *           ������ΪԤ����ָ�����ƣ����Դ������ļ�����
  *           ��ȷ����������ȷ���������κβ��죬�����жϺ��������ܽ���
  */
void USART1_IRQHandler(void)
{
    if (USART_GetITStatus(USART1, USART_IT_RXNE) == SET) //�ж��Ƿ���USART1�Ľ����¼��������ж�
    {
        UART1_rx_dataframe(); //���ô���1��������֡��������
        USART_ClearITPendingBit(USART1, USART_IT_RXNE); //���USART1��RXNE��־λ
        //��ȡ���ݼĴ������Զ�����˱�־λ
        //����Ѿ���ȡ�����ݼĴ�����Ҳ���Բ�ִ�д˴���
    }
}
/**
  * ��    ����USART2�жϺ���
  * ��    ������
  * �� �� ֵ����
  * ע������˺���Ϊ�жϺ�����������ã��жϴ������Զ�ִ��
  *           ������ΪԤ����ָ�����ƣ����Դ������ļ�����
  *           ��ȷ����������ȷ���������κβ��죬�����жϺ��������ܽ���
  */
void USART2_IRQHandler(void)
{
    if (USART_GetITStatus(USART2, USART_IT_RXNE) == SET) //�ж��Ƿ���USART2�Ľ����¼��������ж�
    {
        UART2_rx_dataframe(); //���ô���2��������֡��������
        USART_ClearITPendingBit(USART2, USART_IT_RXNE); //���USART2��RXNE��־λ
        //��ȡ���ݼĴ������Զ�����˱�־λ
        //����Ѿ���ȡ�����ݼĴ�����Ҳ���Բ�ִ�д˴���
    }
}
/**
  * ��    ����USART3�жϺ���
  * ��    ������
  * �� �� ֵ����
  * ע������˺���Ϊ�жϺ�����������ã��жϴ������Զ�ִ��
  *           ������ΪԤ����ָ�����ƣ����Դ������ļ�����
  *           ��ȷ����������ȷ���������κβ��죬�����жϺ��������ܽ���
  */
void USART3_IRQHandler(void)
{
    if (USART_GetITStatus(USART3, USART_IT_RXNE) == SET) //�ж��Ƿ���USART3�Ľ����¼��������ж�
    {
        UART3_rx_dataframe(); //���ô���3��������֡��������
        USART_ClearITPendingBit(USART3, USART_IT_RXNE); //���USART3��RXNE��־λ
        //��ȡ���ݼĴ������Զ�����˱�־λ
        //����Ѿ���ȡ�����ݼĴ�����Ҳ���Բ�ִ�д˴���
    }
}

// ������������ݲ����������ݰ�
// StepperPacket_t ���ݰ��ṹ˵����
// header    : ��ͷ�������� 0x7b �� MY_UART2_HEADER
// id        : �����ţ�01 ��ʾ����2��02 ��ʾ����3
// ctrl_mode : ����ģʽ���������һ��Ϊ 2
// dir       : ����0 ��ת��1 ��ת
// microstep : ϸ�������������ϸ�֣����� 32
// angle_h   : Ŀ��Ƕȸ�8λ��int16_t�����ֽڣ�
// angle_l   : Ŀ��Ƕȵ�8λ��int16_t�����ֽڣ�
// speed_h   : �ٶȸ�8λ��uint16_t�����ֽڣ�
// speed_l   : �ٶȵ�8λ��uint16_t�����ֽڣ�
// checksum  : У��λ��ǰ9�ֽ����
// tail      : ��β�������� 0x7d �� MY_UART2_TAIL
void StepperPacket_Fill(StepperPacket_t* pkt, uint8_t id, uint8_t ctrl_mode, uint8_t dir, uint8_t microstep, int16_t angle, uint16_t speed) 
{
    pkt->header    = MY_UART2_HEADER ; // ��ͷ
    pkt->id        = id;               // ������
    pkt->ctrl_mode = ctrl_mode;        // ����ģʽ
    pkt->dir       = dir;              // ����
    pkt->microstep = microstep;        // ϸ����
    pkt->angle_h   = (angle >> 8) & 0xFF; // �Ƕȸ�8λ
    pkt->angle_l   = angle & 0xFF;        // �Ƕȵ�8λ
    pkt->speed_h   = (speed >> 8) & 0xFF; // �ٶȸ�8λ
    pkt->speed_l   = speed & 0xFF;        // �ٶȵ�8λ
    // У��λ�����ǰ9�ֽ�
    uint8_t sum = pkt->header ^ pkt->id ^ pkt->ctrl_mode ^ pkt->dir ^ pkt->microstep ^ pkt->angle_h ^ pkt->angle_l ^ pkt->speed_h ^ pkt->speed_l;
    pkt->checksum = sum;                // У��λ
    pkt->tail     = MY_UART2_TAIL;      // ��β
}

// ����2���ͱ��01������ݰ�
void USART2_SendStepperPacket(const StepperPacket_t* pkt)
 {
    const uint8_t* p = (const uint8_t*)pkt;
    int i;
    for(i=0; i<sizeof(StepperPacket_t); i++) {
        while (USART_GetFlagStatus(USART2, USART_FLAG_TXE) == RESET);
        USART_SendData(USART2, p[i]);
        while (USART_GetFlagStatus(USART2, USART_FLAG_TC) == RESET);
    }
}

// ����3���ͱ��02������ݰ�
void USART3_SendStepperPacket(const StepperPacket_t* pkt)
 {
    const uint8_t* p = (const uint8_t*)pkt;
    int i;
    for(i=0; i<sizeof(StepperPacket_t); i++) {
        while (USART_GetFlagStatus(USART3, USART_FLAG_TXE) == RESET);
        USART_SendData(USART3, p[i]);
        while (USART_GetFlagStatus(USART3, USART_FLAG_TC) == RESET);
    }
}
